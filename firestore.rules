rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Helper function to check if user owns the resource
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    // Helper function to validate transaction data
    function isValidTransaction(transaction) {
      return transaction.keys().hasAll(['id', 'amount', 'date', 'categoryType', 'transactionType', 'category', 'userId', 'createdAt', 'updatedAt', 'version']) &&
             transaction.amount is number &&
             transaction.amount > 0 &&
             transaction.categoryType is string &&
             transaction.transactionType is string &&
             transaction.userId is string &&
             transaction.version is int &&
             transaction.version > 0;
    }
    
    // Helper function to validate category data
    function isValidCategory(category) {
      return category.keys().hasAll(['id', 'categoryName', 'categoryType', 'type', 'userId', 'createdAt', 'updatedAt', 'version']) &&
             category.categoryName is string &&
             category.categoryName.size() > 0 &&
             category.categoryType is string &&
             category.type is string &&
             category.userId is string &&
             category.version is int &&
             category.version > 0;
    }
    
    // Transactions collection rules
    match /transactions/{transactionId} {
      // Allow read if user is authenticated and owns the transaction
      allow read: if isAuthenticated() && isOwner(resource.data.userId);
      
      // Allow create if user is authenticated, owns the transaction, and data is valid
      allow create: if isAuthenticated() && 
                   isOwner(request.resource.data.userId) &&
                   isValidTransaction(request.resource.data) &&
                   request.resource.data.userId == request.auth.uid;
      
      // Allow update if user is authenticated, owns the transaction, data is valid,
      // and version is incremented
      allow update: if isAuthenticated() && 
                   isOwner(resource.data.userId) &&
                   isOwner(request.resource.data.userId) &&
                   isValidTransaction(request.resource.data) &&
                   request.resource.data.version > resource.data.version &&
                   request.resource.data.userId == resource.data.userId;
      
      // Allow delete if user is authenticated and owns the transaction
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }
    
    // Categories collection rules
    match /categories/{categoryId} {
      // Allow read if user is authenticated and owns the category
      allow read: if isAuthenticated() && isOwner(resource.data.userId);
      
      // Allow create if user is authenticated, owns the category, and data is valid
      allow create: if isAuthenticated() && 
                   isOwner(request.resource.data.userId) &&
                   isValidCategory(request.resource.data) &&
                   request.resource.data.userId == request.auth.uid;
      
      // Allow update if user is authenticated, owns the category, data is valid,
      // and version is incremented (for soft deletes and updates)
      allow update: if isAuthenticated() && 
                   isOwner(resource.data.userId) &&
                   isOwner(request.resource.data.userId) &&
                   isValidCategory(request.resource.data) &&
                   request.resource.data.version > resource.data.version &&
                   request.resource.data.userId == resource.data.userId;
      
      // Allow delete if user is authenticated and owns the category
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }
    
    // User profile collection (if needed in the future)
    match /users/{userId} {
      // Allow read/write only for the user's own profile
      allow read, write: if isAuthenticated() && isOwner(userId);
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}

// Additional rules for Firebase Storage (if used for file uploads)
// service firebase.storage {
//   match /b/{bucket}/o {
//     match /users/{userId}/{allPaths=**} {
//       // Allow read/write only for authenticated users accessing their own files
//       allow read, write: if request.auth != null && request.auth.uid == userId;
//     }
//   }
// }
