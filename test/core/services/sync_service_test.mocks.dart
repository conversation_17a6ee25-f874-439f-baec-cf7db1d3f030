// // Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// // in money_track/test/core/services/sync_service_test.dart.
// // Do not manually edit this file.

// // ignore_for_file: no_leading_underscores_for_library_prefixes
// import 'dart:async' as _i4;
// import 'dart:typed_data' as _i16;

// import 'package:hive/src/box/default_compaction_strategy.dart' as _i15;
// import 'package:hive/src/box/default_key_comparator.dart' as _i14;
// import 'package:hive_flutter/hive_flutter.dart' as _i2;
// import 'package:mockito/mockito.dart' as _i1;
// import 'package:mockito/src/dummies.dart' as _i6;
// import 'package:money_track/core/services/connectivity_service.dart' as _i13;
// import 'package:money_track/data/datasources/local/category_local_datasource.dart'
//     as _i7;
// import 'package:money_track/data/datasources/local/transaction_local_datasource.dart'
//     as _i3;
// import 'package:money_track/data/datasources/remote/category_remote_datasource.dart'
//     as _i11;
// import 'package:money_track/data/datasources/remote/transaction_remote_datasource.dart'
//     as _i9;
// import 'package:money_track/data/models/category_model.dart' as _i8;
// import 'package:money_track/data/models/firestore/category_firestore_model.dart'
//     as _i12;
// import 'package:money_track/data/models/firestore/transaction_firestore_model.dart'
//     as _i10;
// import 'package:money_track/data/models/sync/sync_operation_model.dart' as _i17;
// import 'package:money_track/data/models/transaction_model.dart' as _i5;

// // ignore_for_file: type=lint
// // ignore_for_file: avoid_redundant_argument_values
// // ignore_for_file: avoid_setters_without_getters
// // ignore_for_file: comment_references
// // ignore_for_file: deprecated_member_use
// // ignore_for_file: deprecated_member_use_from_same_package
// // ignore_for_file: implementation_imports
// // ignore_for_file: invalid_use_of_visible_for_testing_member
// // ignore_for_file: must_be_immutable
// // ignore_for_file: prefer_const_constructors
// // ignore_for_file: unnecessary_parenthesis
// // ignore_for_file: camel_case_types
// // ignore_for_file: subtype_of_sealed_class

// class _FakeBox_0<E1> extends _i1.SmartFake implements _i2.Box<E1> {
//   _FakeBox_0(
//     Object parent,
//     Invocation parentInvocation,
//   ) : super(
//           parent,
//           parentInvocation,
//         );
// }

// class _FakeLazyBox_1<E1> extends _i1.SmartFake implements _i2.LazyBox<E1> {
//   _FakeLazyBox_1(
//     Object parent,
//     Invocation parentInvocation,
//   ) : super(
//           parent,
//           parentInvocation,
//         );
// }

// /// A class which mocks [TransactionLocalDataSource].
// ///
// /// See the documentation for Mockito's code generation for more information.
// class MockTransactionLocalDataSource extends _i1.Mock
//     implements _i3.TransactionLocalDataSource {
//   MockTransactionLocalDataSource() {
//     _i1.throwOnMissingStub(this);
//   }

//   @override
//   _i4.Future<List<_i5.TransactionModel>> getAllTransactions() =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #getAllTransactions,
//           [],
//         ),
//         returnValue: _i4.Future<List<_i5.TransactionModel>>.value(
//             <_i5.TransactionModel>[]),
//       ) as _i4.Future<List<_i5.TransactionModel>>);

//   @override
//   _i4.Future<String> addTransaction(_i5.TransactionModel? transaction) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #addTransaction,
//           [transaction],
//         ),
//         returnValue: _i4.Future<String>.value(_i6.dummyValue<String>(
//           this,
//           Invocation.method(
//             #addTransaction,
//             [transaction],
//           ),
//         )),
//       ) as _i4.Future<String>);

//   @override
//   _i4.Future<String> editTransaction(_i5.TransactionModel? transaction) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #editTransaction,
//           [transaction],
//         ),
//         returnValue: _i4.Future<String>.value(_i6.dummyValue<String>(
//           this,
//           Invocation.method(
//             #editTransaction,
//             [transaction],
//           ),
//         )),
//       ) as _i4.Future<String>);

//   @override
//   _i4.Future<void> deleteTransaction(String? transactionId) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #deleteTransaction,
//           [transactionId],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);
// }

// /// A class which mocks [CategoryLocalDataSource].
// ///
// /// See the documentation for Mockito's code generation for more information.
// class MockCategoryLocalDataSource extends _i1.Mock
//     implements _i7.CategoryLocalDataSource {
//   MockCategoryLocalDataSource() {
//     _i1.throwOnMissingStub(this);
//   }

//   @override
//   _i4.Future<List<_i8.CategoryModel>> getAllCategories() => (super.noSuchMethod(
//         Invocation.method(
//           #getAllCategories,
//           [],
//         ),
//         returnValue:
//             _i4.Future<List<_i8.CategoryModel>>.value(<_i8.CategoryModel>[]),
//       ) as _i4.Future<List<_i8.CategoryModel>>);

//   @override
//   _i4.Future<String> addCategory(_i8.CategoryModel? category) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #addCategory,
//           [category],
//         ),
//         returnValue: _i4.Future<String>.value(_i6.dummyValue<String>(
//           this,
//           Invocation.method(
//             #addCategory,
//             [category],
//           ),
//         )),
//       ) as _i4.Future<String>);

//   @override
//   _i4.Future<void> deleteCategory(String? categoryId) => (super.noSuchMethod(
//         Invocation.method(
//           #deleteCategory,
//           [categoryId],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<void> setDefaultCategories() => (super.noSuchMethod(
//         Invocation.method(
//           #setDefaultCategories,
//           [],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);
// }

// /// A class which mocks [TransactionRemoteDataSource].
// ///
// /// See the documentation for Mockito's code generation for more information.
// class MockTransactionRemoteDataSource extends _i1.Mock
//     implements _i9.TransactionRemoteDataSource {
//   MockTransactionRemoteDataSource() {
//     _i1.throwOnMissingStub(this);
//   }

//   @override
//   _i4.Future<List<_i10.TransactionFirestoreModel>> getAllTransactions(
//           String? userId) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #getAllTransactions,
//           [userId],
//         ),
//         returnValue: _i4.Future<List<_i10.TransactionFirestoreModel>>.value(
//             <_i10.TransactionFirestoreModel>[]),
//       ) as _i4.Future<List<_i10.TransactionFirestoreModel>>);

//   @override
//   _i4.Future<String> addTransaction(
//           _i10.TransactionFirestoreModel? transaction) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #addTransaction,
//           [transaction],
//         ),
//         returnValue: _i4.Future<String>.value(_i6.dummyValue<String>(
//           this,
//           Invocation.method(
//             #addTransaction,
//             [transaction],
//           ),
//         )),
//       ) as _i4.Future<String>);

//   @override
//   _i4.Future<String> updateTransaction(
//           _i10.TransactionFirestoreModel? transaction) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #updateTransaction,
//           [transaction],
//         ),
//         returnValue: _i4.Future<String>.value(_i6.dummyValue<String>(
//           this,
//           Invocation.method(
//             #updateTransaction,
//             [transaction],
//           ),
//         )),
//       ) as _i4.Future<String>);

//   @override
//   _i4.Future<void> deleteTransaction(
//     String? transactionId,
//     String? userId,
//   ) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #deleteTransaction,
//           [
//             transactionId,
//             userId,
//           ],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<_i10.TransactionFirestoreModel?> getTransaction(
//     String? transactionId,
//     String? userId,
//   ) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #getTransaction,
//           [
//             transactionId,
//             userId,
//           ],
//         ),
//         returnValue: _i4.Future<_i10.TransactionFirestoreModel?>.value(),
//       ) as _i4.Future<_i10.TransactionFirestoreModel?>);

//   @override
//   _i4.Stream<List<_i10.TransactionFirestoreModel>> getTransactionsStream(
//           String? userId) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #getTransactionsStream,
//           [userId],
//         ),
//         returnValue: _i4.Stream<List<_i10.TransactionFirestoreModel>>.empty(),
//       ) as _i4.Stream<List<_i10.TransactionFirestoreModel>>);

//   @override
//   _i4.Future<void> batchWrite(
//     List<_i10.TransactionFirestoreModel>? transactions,
//     String? userId,
//   ) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #batchWrite,
//           [
//             transactions,
//             userId,
//           ],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);
// }

// /// A class which mocks [CategoryRemoteDataSource].
// ///
// /// See the documentation for Mockito's code generation for more information.
// class MockCategoryRemoteDataSource extends _i1.Mock
//     implements _i11.CategoryRemoteDataSource {
//   MockCategoryRemoteDataSource() {
//     _i1.throwOnMissingStub(this);
//   }

//   @override
//   _i4.Future<List<_i12.CategoryFirestoreModel>> getAllCategories(
//           String? userId) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #getAllCategories,
//           [userId],
//         ),
//         returnValue: _i4.Future<List<_i12.CategoryFirestoreModel>>.value(
//             <_i12.CategoryFirestoreModel>[]),
//       ) as _i4.Future<List<_i12.CategoryFirestoreModel>>);

//   @override
//   _i4.Future<String> addCategory(_i12.CategoryFirestoreModel? category) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #addCategory,
//           [category],
//         ),
//         returnValue: _i4.Future<String>.value(_i6.dummyValue<String>(
//           this,
//           Invocation.method(
//             #addCategory,
//             [category],
//           ),
//         )),
//       ) as _i4.Future<String>);

//   @override
//   _i4.Future<String> updateCategory(_i12.CategoryFirestoreModel? category) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #updateCategory,
//           [category],
//         ),
//         returnValue: _i4.Future<String>.value(_i6.dummyValue<String>(
//           this,
//           Invocation.method(
//             #updateCategory,
//             [category],
//           ),
//         )),
//       ) as _i4.Future<String>);

//   @override
//   _i4.Future<void> deleteCategory(
//     String? categoryId,
//     String? userId,
//   ) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #deleteCategory,
//           [
//             categoryId,
//             userId,
//           ],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<_i12.CategoryFirestoreModel?> getCategory(
//     String? categoryId,
//     String? userId,
//   ) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #getCategory,
//           [
//             categoryId,
//             userId,
//           ],
//         ),
//         returnValue: _i4.Future<_i12.CategoryFirestoreModel?>.value(),
//       ) as _i4.Future<_i12.CategoryFirestoreModel?>);

//   @override
//   _i4.Stream<List<_i12.CategoryFirestoreModel>> getCategoriesStream(
//           String? userId) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #getCategoriesStream,
//           [userId],
//         ),
//         returnValue: _i4.Stream<List<_i12.CategoryFirestoreModel>>.empty(),
//       ) as _i4.Stream<List<_i12.CategoryFirestoreModel>>);

//   @override
//   _i4.Future<void> batchWrite(
//     List<_i12.CategoryFirestoreModel>? categories,
//     String? userId,
//   ) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #batchWrite,
//           [
//             categories,
//             userId,
//           ],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<void> setDefaultCategories(String? userId) => (super.noSuchMethod(
//         Invocation.method(
//           #setDefaultCategories,
//           [userId],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);
// }

// /// A class which mocks [ConnectivityService].
// ///
// /// See the documentation for Mockito's code generation for more information.
// class MockConnectivityService extends _i1.Mock
//     implements _i13.ConnectivityService {
//   MockConnectivityService() {
//     _i1.throwOnMissingStub(this);
//   }

//   @override
//   bool get isConnected => (super.noSuchMethod(
//         Invocation.getter(#isConnected),
//         returnValue: false,
//       ) as bool);

//   @override
//   _i4.Stream<bool> get connectivityStream => (super.noSuchMethod(
//         Invocation.getter(#connectivityStream),
//         returnValue: _i4.Stream<bool>.empty(),
//       ) as _i4.Stream<bool>);

//   @override
//   _i4.Future<void> initialize() => (super.noSuchMethod(
//         Invocation.method(
//           #initialize,
//           [],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<void> waitForConnection(
//           {Duration? timeout = const Duration(seconds: 30)}) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #waitForConnection,
//           [],
//           {#timeout: timeout},
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<bool> hasInternetConnection() => (super.noSuchMethod(
//         Invocation.method(
//           #hasInternetConnection,
//           [],
//         ),
//         returnValue: _i4.Future<bool>.value(false),
//       ) as _i4.Future<bool>);

//   @override
//   void dispose() => super.noSuchMethod(
//         Invocation.method(
//           #dispose,
//           [],
//         ),
//         returnValueForMissingStub: null,
//       );
// }

// /// A class which mocks [HiveInterface].
// ///
// /// See the documentation for Mockito's code generation for more information.
// class MockHiveInterface extends _i1.Mock implements _i2.HiveInterface {
//   MockHiveInterface() {
//     _i1.throwOnMissingStub(this);
//   }

//   @override
//   void init(
//     String? path, {
//     _i2.HiveStorageBackendPreference? backendPreference =
//         _i2.HiveStorageBackendPreference.native,
//   }) =>
//       super.noSuchMethod(
//         Invocation.method(
//           #init,
//           [path],
//           {#backendPreference: backendPreference},
//         ),
//         returnValueForMissingStub: null,
//       );

//   @override
//   _i4.Future<_i2.Box<E>> openBox<E>(
//     String? name, {
//     _i2.HiveCipher? encryptionCipher,
//     _i2.KeyComparator? keyComparator = _i14.defaultKeyComparator,
//     _i2.CompactionStrategy? compactionStrategy = _i15.defaultCompactionStrategy,
//     bool? crashRecovery = true,
//     String? path,
//     _i16.Uint8List? bytes,
//     String? collection,
//     List<int>? encryptionKey,
//   }) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #openBox,
//           [name],
//           {
//             #encryptionCipher: encryptionCipher,
//             #keyComparator: keyComparator,
//             #compactionStrategy: compactionStrategy,
//             #crashRecovery: crashRecovery,
//             #path: path,
//             #bytes: bytes,
//             #collection: collection,
//             #encryptionKey: encryptionKey,
//           },
//         ),
//         returnValue: _i4.Future<_i2.Box<E>>.value(_FakeBox_0<E>(
//           this,
//           Invocation.method(
//             #openBox,
//             [name],
//             {
//               #encryptionCipher: encryptionCipher,
//               #keyComparator: keyComparator,
//               #compactionStrategy: compactionStrategy,
//               #crashRecovery: crashRecovery,
//               #path: path,
//               #bytes: bytes,
//               #collection: collection,
//               #encryptionKey: encryptionKey,
//             },
//           ),
//         )),
//       ) as _i4.Future<_i2.Box<E>>);

//   @override
//   _i4.Future<_i2.LazyBox<E>> openLazyBox<E>(
//     String? name, {
//     _i2.HiveCipher? encryptionCipher,
//     _i2.KeyComparator? keyComparator = _i14.defaultKeyComparator,
//     _i2.CompactionStrategy? compactionStrategy = _i15.defaultCompactionStrategy,
//     bool? crashRecovery = true,
//     String? path,
//     String? collection,
//     List<int>? encryptionKey,
//   }) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #openLazyBox,
//           [name],
//           {
//             #encryptionCipher: encryptionCipher,
//             #keyComparator: keyComparator,
//             #compactionStrategy: compactionStrategy,
//             #crashRecovery: crashRecovery,
//             #path: path,
//             #collection: collection,
//             #encryptionKey: encryptionKey,
//           },
//         ),
//         returnValue: _i4.Future<_i2.LazyBox<E>>.value(_FakeLazyBox_1<E>(
//           this,
//           Invocation.method(
//             #openLazyBox,
//             [name],
//             {
//               #encryptionCipher: encryptionCipher,
//               #keyComparator: keyComparator,
//               #compactionStrategy: compactionStrategy,
//               #crashRecovery: crashRecovery,
//               #path: path,
//               #collection: collection,
//               #encryptionKey: encryptionKey,
//             },
//           ),
//         )),
//       ) as _i4.Future<_i2.LazyBox<E>>);

//   @override
//   _i2.Box<E> box<E>(String? name) => (super.noSuchMethod(
//         Invocation.method(
//           #box,
//           [name],
//         ),
//         returnValue: _FakeBox_0<E>(
//           this,
//           Invocation.method(
//             #box,
//             [name],
//           ),
//         ),
//       ) as _i2.Box<E>);

//   @override
//   _i2.LazyBox<E> lazyBox<E>(String? name) => (super.noSuchMethod(
//         Invocation.method(
//           #lazyBox,
//           [name],
//         ),
//         returnValue: _FakeLazyBox_1<E>(
//           this,
//           Invocation.method(
//             #lazyBox,
//             [name],
//           ),
//         ),
//       ) as _i2.LazyBox<E>);

//   @override
//   bool isBoxOpen(String? name) => (super.noSuchMethod(
//         Invocation.method(
//           #isBoxOpen,
//           [name],
//         ),
//         returnValue: false,
//       ) as bool);

//   @override
//   _i4.Future<void> close() => (super.noSuchMethod(
//         Invocation.method(
//           #close,
//           [],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<void> deleteBoxFromDisk(
//     String? name, {
//     String? path,
//   }) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #deleteBoxFromDisk,
//           [name],
//           {#path: path},
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<void> deleteFromDisk() => (super.noSuchMethod(
//         Invocation.method(
//           #deleteFromDisk,
//           [],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   List<int> generateSecureKey() => (super.noSuchMethod(
//         Invocation.method(
//           #generateSecureKey,
//           [],
//         ),
//         returnValue: <int>[],
//       ) as List<int>);

//   @override
//   _i4.Future<bool> boxExists(
//     String? name, {
//     String? path,
//   }) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #boxExists,
//           [name],
//           {#path: path},
//         ),
//         returnValue: _i4.Future<bool>.value(false),
//       ) as _i4.Future<bool>);

//   @override
//   void resetAdapters() => super.noSuchMethod(
//         Invocation.method(
//           #resetAdapters,
//           [],
//         ),
//         returnValueForMissingStub: null,
//       );

//   @override
//   void registerAdapter<T>(
//     _i2.TypeAdapter<T>? adapter, {
//     bool? internal = false,
//     bool? override = false,
//   }) =>
//       super.noSuchMethod(
//         Invocation.method(
//           #registerAdapter,
//           [adapter],
//           {
//             #internal: internal,
//             #override: override,
//           },
//         ),
//         returnValueForMissingStub: null,
//       );

//   @override
//   bool isAdapterRegistered(int? typeId) => (super.noSuchMethod(
//         Invocation.method(
//           #isAdapterRegistered,
//           [typeId],
//         ),
//         returnValue: false,
//       ) as bool);

//   @override
//   void ignoreTypeId<T>(int? typeId) => super.noSuchMethod(
//         Invocation.method(
//           #ignoreTypeId,
//           [typeId],
//         ),
//         returnValueForMissingStub: null,
//       );
// }

// /// A class which mocks [Box].
// ///
// /// See the documentation for Mockito's code generation for more information.
// class MockSyncOperationBox extends _i1.Mock
//     implements _i2.Box<_i17.SyncOperationModel> {
//   MockSyncOperationBox() {
//     _i1.throwOnMissingStub(this);
//   }

//   @override
//   Iterable<_i17.SyncOperationModel> get values => (super.noSuchMethod(
//         Invocation.getter(#values),
//         returnValue: <_i17.SyncOperationModel>[],
//       ) as Iterable<_i17.SyncOperationModel>);

//   @override
//   String get name => (super.noSuchMethod(
//         Invocation.getter(#name),
//         returnValue: _i6.dummyValue<String>(
//           this,
//           Invocation.getter(#name),
//         ),
//       ) as String);

//   @override
//   bool get isOpen => (super.noSuchMethod(
//         Invocation.getter(#isOpen),
//         returnValue: false,
//       ) as bool);

//   @override
//   bool get lazy => (super.noSuchMethod(
//         Invocation.getter(#lazy),
//         returnValue: false,
//       ) as bool);

//   @override
//   Iterable<dynamic> get keys => (super.noSuchMethod(
//         Invocation.getter(#keys),
//         returnValue: <dynamic>[],
//       ) as Iterable<dynamic>);

//   @override
//   int get length => (super.noSuchMethod(
//         Invocation.getter(#length),
//         returnValue: 0,
//       ) as int);

//   @override
//   bool get isEmpty => (super.noSuchMethod(
//         Invocation.getter(#isEmpty),
//         returnValue: false,
//       ) as bool);

//   @override
//   bool get isNotEmpty => (super.noSuchMethod(
//         Invocation.getter(#isNotEmpty),
//         returnValue: false,
//       ) as bool);

//   @override
//   Iterable<_i17.SyncOperationModel> valuesBetween({
//     dynamic startKey,
//     dynamic endKey,
//   }) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #valuesBetween,
//           [],
//           {
//             #startKey: startKey,
//             #endKey: endKey,
//           },
//         ),
//         returnValue: <_i17.SyncOperationModel>[],
//       ) as Iterable<_i17.SyncOperationModel>);

//   @override
//   _i17.SyncOperationModel? getAt(int? index) =>
//       (super.noSuchMethod(Invocation.method(
//         #getAt,
//         [index],
//       )) as _i17.SyncOperationModel?);

//   @override
//   Map<dynamic, _i17.SyncOperationModel> toMap() => (super.noSuchMethod(
//         Invocation.method(
//           #toMap,
//           [],
//         ),
//         returnValue: <dynamic, _i17.SyncOperationModel>{},
//       ) as Map<dynamic, _i17.SyncOperationModel>);

//   @override
//   dynamic keyAt(int? index) => super.noSuchMethod(Invocation.method(
//         #keyAt,
//         [index],
//       ));

//   @override
//   _i4.Stream<_i2.BoxEvent> watch({dynamic key}) => (super.noSuchMethod(
//         Invocation.method(
//           #watch,
//           [],
//           {#key: key},
//         ),
//         returnValue: _i4.Stream<_i2.BoxEvent>.empty(),
//       ) as _i4.Stream<_i2.BoxEvent>);

//   @override
//   bool containsKey(dynamic key) => (super.noSuchMethod(
//         Invocation.method(
//           #containsKey,
//           [key],
//         ),
//         returnValue: false,
//       ) as bool);

//   @override
//   _i4.Future<void> put(
//     dynamic key,
//     _i17.SyncOperationModel? value,
//   ) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #put,
//           [
//             key,
//             value,
//           ],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<void> putAt(
//     int? index,
//     _i17.SyncOperationModel? value,
//   ) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #putAt,
//           [
//             index,
//             value,
//           ],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<void> putAll(Map<dynamic, _i17.SyncOperationModel>? entries) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #putAll,
//           [entries],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<int> add(_i17.SyncOperationModel? value) => (super.noSuchMethod(
//         Invocation.method(
//           #add,
//           [value],
//         ),
//         returnValue: _i4.Future<int>.value(0),
//       ) as _i4.Future<int>);

//   @override
//   _i4.Future<Iterable<int>> addAll(Iterable<_i17.SyncOperationModel>? values) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #addAll,
//           [values],
//         ),
//         returnValue: _i4.Future<Iterable<int>>.value(<int>[]),
//       ) as _i4.Future<Iterable<int>>);

//   @override
//   _i4.Future<void> delete(dynamic key) => (super.noSuchMethod(
//         Invocation.method(
//           #delete,
//           [key],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<void> deleteAt(int? index) => (super.noSuchMethod(
//         Invocation.method(
//           #deleteAt,
//           [index],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<void> deleteAll(Iterable<dynamic>? keys) => (super.noSuchMethod(
//         Invocation.method(
//           #deleteAll,
//           [keys],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<void> compact() => (super.noSuchMethod(
//         Invocation.method(
//           #compact,
//           [],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<int> clear() => (super.noSuchMethod(
//         Invocation.method(
//           #clear,
//           [],
//         ),
//         returnValue: _i4.Future<int>.value(0),
//       ) as _i4.Future<int>);

//   @override
//   _i4.Future<void> close() => (super.noSuchMethod(
//         Invocation.method(
//           #close,
//           [],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<void> deleteFromDisk() => (super.noSuchMethod(
//         Invocation.method(
//           #deleteFromDisk,
//           [],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<void> flush() => (super.noSuchMethod(
//         Invocation.method(
//           #flush,
//           [],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);
// }

// /// A class which mocks [Box].
// ///
// /// See the documentation for Mockito's code generation for more information.
// class MockGenericBox<E> extends _i1.Mock implements _i2.Box<E> {
//   MockGenericBox() {
//     _i1.throwOnMissingStub(this);
//   }

//   @override
//   Iterable<E> get values => (super.noSuchMethod(
//         Invocation.getter(#values),
//         returnValue: <E>[],
//       ) as Iterable<E>);

//   @override
//   String get name => (super.noSuchMethod(
//         Invocation.getter(#name),
//         returnValue: _i6.dummyValue<String>(
//           this,
//           Invocation.getter(#name),
//         ),
//       ) as String);

//   @override
//   bool get isOpen => (super.noSuchMethod(
//         Invocation.getter(#isOpen),
//         returnValue: false,
//       ) as bool);

//   @override
//   bool get lazy => (super.noSuchMethod(
//         Invocation.getter(#lazy),
//         returnValue: false,
//       ) as bool);

//   @override
//   Iterable<dynamic> get keys => (super.noSuchMethod(
//         Invocation.getter(#keys),
//         returnValue: <dynamic>[],
//       ) as Iterable<dynamic>);

//   @override
//   int get length => (super.noSuchMethod(
//         Invocation.getter(#length),
//         returnValue: 0,
//       ) as int);

//   @override
//   bool get isEmpty => (super.noSuchMethod(
//         Invocation.getter(#isEmpty),
//         returnValue: false,
//       ) as bool);

//   @override
//   bool get isNotEmpty => (super.noSuchMethod(
//         Invocation.getter(#isNotEmpty),
//         returnValue: false,
//       ) as bool);

//   @override
//   Iterable<E> valuesBetween({
//     dynamic startKey,
//     dynamic endKey,
//   }) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #valuesBetween,
//           [],
//           {
//             #startKey: startKey,
//             #endKey: endKey,
//           },
//         ),
//         returnValue: <E>[],
//       ) as Iterable<E>);

//   @override
//   E? getAt(int? index) => (super.noSuchMethod(Invocation.method(
//         #getAt,
//         [index],
//       )) as E?);

//   @override
//   Map<dynamic, E> toMap() => (super.noSuchMethod(
//         Invocation.method(
//           #toMap,
//           [],
//         ),
//         returnValue: <dynamic, E>{},
//       ) as Map<dynamic, E>);

//   @override
//   dynamic keyAt(int? index) => super.noSuchMethod(Invocation.method(
//         #keyAt,
//         [index],
//       ));

//   @override
//   _i4.Stream<_i2.BoxEvent> watch({dynamic key}) => (super.noSuchMethod(
//         Invocation.method(
//           #watch,
//           [],
//           {#key: key},
//         ),
//         returnValue: _i4.Stream<_i2.BoxEvent>.empty(),
//       ) as _i4.Stream<_i2.BoxEvent>);

//   @override
//   bool containsKey(dynamic key) => (super.noSuchMethod(
//         Invocation.method(
//           #containsKey,
//           [key],
//         ),
//         returnValue: false,
//       ) as bool);

//   @override
//   _i4.Future<void> put(
//     dynamic key,
//     E? value,
//   ) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #put,
//           [
//             key,
//             value,
//           ],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<void> putAt(
//     int? index,
//     E? value,
//   ) =>
//       (super.noSuchMethod(
//         Invocation.method(
//           #putAt,
//           [
//             index,
//             value,
//           ],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<void> putAll(Map<dynamic, E>? entries) => (super.noSuchMethod(
//         Invocation.method(
//           #putAll,
//           [entries],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<int> add(E? value) => (super.noSuchMethod(
//         Invocation.method(
//           #add,
//           [value],
//         ),
//         returnValue: _i4.Future<int>.value(0),
//       ) as _i4.Future<int>);

//   @override
//   _i4.Future<Iterable<int>> addAll(Iterable<E>? values) => (super.noSuchMethod(
//         Invocation.method(
//           #addAll,
//           [values],
//         ),
//         returnValue: _i4.Future<Iterable<int>>.value(<int>[]),
//       ) as _i4.Future<Iterable<int>>);

//   @override
//   _i4.Future<void> delete(dynamic key) => (super.noSuchMethod(
//         Invocation.method(
//           #delete,
//           [key],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<void> deleteAt(int? index) => (super.noSuchMethod(
//         Invocation.method(
//           #deleteAt,
//           [index],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<void> deleteAll(Iterable<dynamic>? keys) => (super.noSuchMethod(
//         Invocation.method(
//           #deleteAll,
//           [keys],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<void> compact() => (super.noSuchMethod(
//         Invocation.method(
//           #compact,
//           [],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<int> clear() => (super.noSuchMethod(
//         Invocation.method(
//           #clear,
//           [],
//         ),
//         returnValue: _i4.Future<int>.value(0),
//       ) as _i4.Future<int>);

//   @override
//   _i4.Future<void> close() => (super.noSuchMethod(
//         Invocation.method(
//           #close,
//           [],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<void> deleteFromDisk() => (super.noSuchMethod(
//         Invocation.method(
//           #deleteFromDisk,
//           [],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);

//   @override
//   _i4.Future<void> flush() => (super.noSuchMethod(
//         Invocation.method(
//           #flush,
//           [],
//         ),
//         returnValue: _i4.Future<void>.value(),
//         returnValueForMissingStub: _i4.Future<void>.value(),
//       ) as _i4.Future<void>);
// }
