// Mocks generated by Mockito 5.4.5 from annotations
// in money_track/test/core/presentation/bloc/sync_cubit_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:mockito/mockito.dart' as _i1;
import 'package:money_track/core/services/connectivity_service.dart' as _i5;
import 'package:money_track/core/services/sync_service.dart' as _i2;
import 'package:money_track/data/models/sync/sync_operation_model.dart' as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeSyncResult_0 extends _i1.SmartFake implements _i2.SyncResult {
  _FakeSyncResult_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [SyncService].
///
/// See the documentation for Mockito's code generation for more information.
class MockSyncService extends _i1.Mock implements _i2.SyncService {
  MockSyncService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.SyncStatus get currentStatus => (super.noSuchMethod(
        Invocation.getter(#currentStatus),
        returnValue: _i2.SyncStatus.idle,
      ) as _i2.SyncStatus);

  @override
  _i3.Stream<_i2.SyncStatus> get syncStatusStream => (super.noSuchMethod(
        Invocation.getter(#syncStatusStream),
        returnValue: _i3.Stream<_i2.SyncStatus>.empty(),
      ) as _i3.Stream<_i2.SyncStatus>);

  @override
  _i3.Stream<_i2.SyncResult> get syncResultStream => (super.noSuchMethod(
        Invocation.getter(#syncResultStream),
        returnValue: _i3.Stream<_i2.SyncResult>.empty(),
      ) as _i3.Stream<_i2.SyncResult>);

  @override
  _i3.Future<void> initializeForUser(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #initializeForUser,
          [userId],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> clearLocalData() => (super.noSuchMethod(
        Invocation.method(
          #clearLocalData,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> queueSyncOperation(_i4.SyncOperationModel? operation) =>
      (super.noSuchMethod(
        Invocation.method(
          #queueSyncOperation,
          [operation],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<_i2.SyncResult> forceSyncNow() => (super.noSuchMethod(
        Invocation.method(
          #forceSyncNow,
          [],
        ),
        returnValue: _i3.Future<_i2.SyncResult>.value(_FakeSyncResult_0(
          this,
          Invocation.method(
            #forceSyncNow,
            [],
          ),
        )),
      ) as _i3.Future<_i2.SyncResult>);

  @override
  _i3.Future<Map<String, dynamic>> getSyncStats() => (super.noSuchMethod(
        Invocation.method(
          #getSyncStats,
          [],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [ConnectivityService].
///
/// See the documentation for Mockito's code generation for more information.
class MockConnectivityService extends _i1.Mock
    implements _i5.ConnectivityService {
  MockConnectivityService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isConnected => (super.noSuchMethod(
        Invocation.getter(#isConnected),
        returnValue: false,
      ) as bool);

  @override
  _i3.Stream<bool> get connectivityStream => (super.noSuchMethod(
        Invocation.getter(#connectivityStream),
        returnValue: _i3.Stream<bool>.empty(),
      ) as _i3.Stream<bool>);

  @override
  _i3.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> waitForConnection(
          {Duration? timeout = const Duration(seconds: 30)}) =>
      (super.noSuchMethod(
        Invocation.method(
          #waitForConnection,
          [],
          {#timeout: timeout},
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<bool> hasInternetConnection() => (super.noSuchMethod(
        Invocation.method(
          #hasInternetConnection,
          [],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
